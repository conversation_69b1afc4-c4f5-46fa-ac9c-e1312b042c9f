<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Guide - Luno Trading Bot</title>
    <meta name="description" content="Complete setup guide for the Luno Trading Bot. Step-by-step instructions for installation and configuration.">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-robot"></i>
                <span>Luno Trading Bot</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="index.html#features" class="nav-link">Features</a></li>
                <li><a href="setup.html" class="nav-link active">Setup</a></li>
                <li><a href="documentation.html" class="nav-link">Docs</a></li>
                <li><a href="dashboard-demo.html" class="nav-link">Demo</a></li>
                <li><a href="https://github.com/amanasmuei/trading-bot-luno" class="nav-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="page-header">
        <div class="container">
            <h1><i class="fas fa-cog"></i> Setup Guide</h1>
            <p>Complete installation and configuration instructions</p>
        </div>
    </section>

    <!-- Content -->
    <section class="setup-content">
        <div class="container">
            <div class="setup-nav">
                <h3>Table of Contents</h3>
                <ul>
                    <li><a href="#prerequisites">Prerequisites</a></li>
                    <li><a href="#installation">Installation</a></li>
                    <li><a href="#configuration">Configuration</a></li>
                    <li><a href="#api-setup">API Setup</a></li>
                    <li><a href="#running">Running the Bot</a></li>
                    <li><a href="#troubleshooting">Troubleshooting</a></li>
                </ul>
            </div>

            <div class="setup-main">
                <!-- Prerequisites -->
                <section id="prerequisites" class="setup-section">
                    <h2><i class="fas fa-list-check"></i> Prerequisites</h2>
                    <p>Before installing the trading bot, ensure you have the following:</p>
                    
                    <div class="requirement-grid">
                        <div class="requirement-card">
                            <i class="fab fa-python"></i>
                            <h4>Python 3.8+</h4>
                            <p>Required for running the bot</p>
                            <a href="https://python.org/downloads/" target="_blank">Download Python</a>
                        </div>
                        <div class="requirement-card">
                            <i class="fab fa-git-alt"></i>
                            <h4>Git</h4>
                            <p>For cloning the repository</p>
                            <a href="https://git-scm.com/downloads" target="_blank">Download Git</a>
                        </div>
                        <div class="requirement-card">
                            <i class="fas fa-key"></i>
                            <h4>Luno Account</h4>
                            <p>With API access enabled</p>
                            <a href="https://www.luno.com/" target="_blank">Create Account</a>
                        </div>
                    </div>
                </section>

                <!-- Installation -->
                <section id="installation" class="setup-section">
                    <h2><i class="fas fa-download"></i> Installation</h2>
                    
                    <div class="step-container">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h3>Clone the Repository</h3>
                                <p>Download the trading bot source code from GitHub:</p>
                                <pre><code class="language-bash">git clone https://github.com/amanasmuei/trading-bot-luno.git
cd trading-bot-luno</code></pre>
                            </div>
                        </div>

                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h3>Run Setup Script</h3>
                                <p>Use the automated setup script to install dependencies:</p>
                                <pre><code class="language-bash"># For automatic setup
python setup.py

# Or use the setup wizard for guided installation
python setup_wizard.py</code></pre>
                                <div class="info-box">
                                    <i class="fas fa-info-circle"></i>
                                    <p>The setup script will automatically create a virtual environment and install all required dependencies.</p>
                                </div>
                            </div>
                        </div>

                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h3>Alternative: Manual Installation</h3>
                                <p>If you prefer manual installation:</p>
                                <pre><code class="language-bash"># Create virtual environment
python -m venv trading_bot_env

# Activate virtual environment
# On Windows:
trading_bot_env\Scripts\activate
# On macOS/Linux:
source trading_bot_env/bin/activate

# Install dependencies
pip install -r requirements.txt</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Configuration -->
                <section id="configuration" class="setup-section">
                    <h2><i class="fas fa-sliders-h"></i> Configuration</h2>
                    
                    <div class="config-section">
                        <h3>Environment Variables</h3>
                        <p>Create a <code>.env</code> file in the project root with your configuration:</p>
                        <pre><code class="language-bash"># Luno API Credentials
LUNO_API_KEY=your_api_key_here
LUNO_API_SECRET=your_api_secret_here

# Trading Configuration
TRADING_PAIR=XBTMYR
DRY_RUN=true
MAX_POSITION_SIZE=10
STOP_LOSS_PERCENTAGE=5
TAKE_PROFIT_PERCENTAGE=10

# Web Dashboard
WEB_PORT=5000
WEB_HOST=localhost</code></pre>
                        
                        <div class="warning-box">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p><strong>Security Warning:</strong> Never commit your .env file to version control. It contains sensitive API credentials.</p>
                        </div>
                    </div>

                    <div class="config-section">
                        <h3>Trading Parameters</h3>
                        <div class="param-grid">
                            <div class="param-card">
                                <h4>TRADING_PAIR</h4>
                                <p>The cryptocurrency pair to trade (e.g., XBTMYR, ETHMYR)</p>
                            </div>
                            <div class="param-card">
                                <h4>DRY_RUN</h4>
                                <p>Set to <code>true</code> for simulation mode, <code>false</code> for live trading</p>
                            </div>
                            <div class="param-card">
                                <h4>MAX_POSITION_SIZE</h4>
                                <p>Maximum percentage of portfolio to use per trade (1-100)</p>
                            </div>
                            <div class="param-card">
                                <h4>STOP_LOSS_PERCENTAGE</h4>
                                <p>Stop loss threshold as percentage (1-50)</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- API Setup -->
                <section id="api-setup" class="setup-section">
                    <h2><i class="fas fa-key"></i> Luno API Setup</h2>
                    
                    <div class="api-steps">
                        <div class="api-step">
                            <h3>1. Create Luno Account</h3>
                            <p>If you don't have a Luno account, <a href="https://www.luno.com/" target="_blank">create one here</a>.</p>
                        </div>
                        
                        <div class="api-step">
                            <h3>2. Generate API Keys</h3>
                            <ol>
                                <li>Log into your Luno account</li>
                                <li>Go to <strong>Settings</strong> → <strong>API</strong></li>
                                <li>Click <strong>Create API Key</strong></li>
                                <li>Set permissions: <strong>Perm_R_Balance</strong>, <strong>Perm_R_Orders</strong>, <strong>Perm_W_Orders</strong></li>
                                <li>Copy your API Key and Secret</li>
                            </ol>
                        </div>
                        
                        <div class="api-step">
                            <h3>3. Test API Connection</h3>
                            <p>Verify your API credentials work:</p>
                            <pre><code class="language-bash">python -m tests.test_api_credentials</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Running -->
                <section id="running" class="setup-section">
                    <h2><i class="fas fa-play"></i> Running the Bot</h2>
                    
                    <div class="run-options">
                        <div class="run-option">
                            <h3><i class="fas fa-rocket"></i> Quick Start</h3>
                            <p>Use the launcher for easy startup:</p>
                            <pre><code class="language-bash">python launcher.py</code></pre>
                        </div>
                        
                        <div class="run-option">
                            <h3><i class="fas fa-terminal"></i> Manual Start</h3>
                            <p>Run components separately:</p>
                            <pre><code class="language-bash"># Start the trading bot
python scripts/run_bot.py

# In another terminal, start the web dashboard
python src/web/dashboard.py</code></pre>
                        </div>
                        
                        <div class="run-option">
                            <h3><i class="fab fa-docker"></i> Docker</h3>
                            <p>Run using Docker:</p>
                            <pre><code class="language-bash"># Build and run with Docker Compose
docker-compose up -d

# Or build manually
docker build -t luno-trading-bot .
docker run -d --env-file .env -p 5000:5000 luno-trading-bot</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Troubleshooting -->
                <section id="troubleshooting" class="setup-section">
                    <h2><i class="fas fa-wrench"></i> Troubleshooting</h2>
                    
                    <div class="troubleshoot-grid">
                        <div class="trouble-item">
                            <h4>API Connection Failed</h4>
                            <ul>
                                <li>Verify API credentials in .env file</li>
                                <li>Check API permissions on Luno</li>
                                <li>Ensure internet connection is stable</li>
                            </ul>
                        </div>
                        
                        <div class="trouble-item">
                            <h4>Module Import Errors</h4>
                            <ul>
                                <li>Activate virtual environment</li>
                                <li>Reinstall dependencies: <code>pip install -r requirements.txt</code></li>
                                <li>Check Python version (3.8+ required)</li>
                            </ul>
                        </div>
                        
                        <div class="trouble-item">
                            <h4>Web Dashboard Not Loading</h4>
                            <ul>
                                <li>Check if port 5000 is available</li>
                                <li>Verify WEB_HOST and WEB_PORT in .env</li>
                                <li>Check firewall settings</li>
                            </ul>
                        </div>
                        
                        <div class="trouble-item">
                            <h4>Trading Not Working</h4>
                            <ul>
                                <li>Ensure DRY_RUN is set correctly</li>
                                <li>Check account balance</li>
                                <li>Verify trading pair is supported</li>
                            </ul>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Luno Trading Bot</h3>
                    <p>Professional automated trading solution for cryptocurrency markets.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#features">Features</a></li>
                        <li><a href="documentation.html">Documentation</a></li>
                        <li><a href="dashboard-demo.html">Demo</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno" target="_blank">GitHub Repository</a></li>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno/issues" target="_blank">Report Issues</a></li>
                        <li><a href="https://www.luno.com/api" target="_blank">Luno API</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Luno Trading Bot. Open source project.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="script.js"></script>
</body>
</html>

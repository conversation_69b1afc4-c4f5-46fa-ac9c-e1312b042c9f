<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luno Trading Bot - Professional Automated Cryptocurrency Trading Solution</title>
    <meta name="description" content="Enterprise-grade automated trading bot for Luno exchange. Advanced risk management, multi-pair support, real-time monitoring, and professional-grade security. Start trading smarter today.">
    <meta name="keywords" content="cryptocurrency trading bot, Luno exchange, automated trading, Bitcoin trading, Ethereum trading, risk management, technical analysis">
    <meta name="author" content="Aman <PERSON>">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://amanasmuei.github.io/trading-bot-luno/">
    <meta property="og:title" content="Luno Trading Bot - Professional Automated Trading">
    <meta property="og:description" content="Enterprise-grade automated trading bot with advanced risk management and multi-pair support">
    <meta property="og:image" content="https://amanasmuei.github.io/trading-bot-luno/assets/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://amanasmuei.github.io/trading-bot-luno/">
    <meta property="twitter:title" content="Luno Trading Bot - Professional Automated Trading">
    <meta property="twitter:description" content="Enterprise-grade automated trading bot with advanced risk management and multi-pair support">
    <meta property="twitter:image" content="https://amanasmuei.github.io/trading-bot-luno/assets/og-image.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">

    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Luno Trading Bot",
        "description": "Professional automated trading bot for Luno cryptocurrency exchange",
        "url": "https://amanasmuei.github.io/trading-bot-luno/",
        "author": {
            "@type": "Person",
            "name": "Aman Asmuei"
        },
        "applicationCategory": "FinanceApplication",
        "operatingSystem": "Cross-platform",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        }
    }
    </script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-robot"></i>
                <span>Luno Trading Bot</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#features" class="nav-link">Features</a></li>
                <li><a href="#setup" class="nav-link">Setup</a></li>
                <li><a href="documentation.html" class="nav-link">Docs</a></li>
                <li><a href="dashboard-demo.html" class="nav-link">Demo</a></li>
                <li><a href="https://github.com/amanasmuei/trading-bot-luno" class="nav-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-shield-check"></i>
                    <span>Enterprise-Grade Security</span>
                </div>
                <h1 class="hero-title">
                    <span class="gradient-text">Professional Trading</span><br>
                    Automation Platform
                </h1>
                <p class="hero-description">
                    Transform your cryptocurrency trading with our enterprise-grade automated bot.
                    Advanced algorithms, institutional-level risk management, and real-time market analysis
                    for the Luno exchange ecosystem.
                </p>
                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-check-circle"></i>
                        <span>Multi-Currency Support</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-check-circle"></i>
                        <span>Advanced Risk Controls</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-check-circle"></i>
                        <span>Real-Time Analytics</span>
                    </div>
                </div>
                <div class="hero-buttons">
                    <a href="#setup" class="btn btn-primary">
                        <i class="fas fa-download"></i>
                        Start Free Trial
                    </a>
                    <a href="dashboard-demo.html" class="btn btn-secondary">
                        <i class="fas fa-chart-line"></i>
                        Live Demo
                    </a>
                    <a href="documentation.html" class="btn btn-outline">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Trading Pairs</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Monitoring</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">99.9%</span>
                        <span class="stat-label">Uptime</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">Open Source</span>
                        <span class="stat-label">& Free</span>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="trading-card">
                    <div class="card-header">
                        <i class="fas fa-chart-line"></i>
                        <span>Live Trading</span>
                        <div class="status-indicator active"></div>
                    </div>
                    <div class="price-display">
                        <span class="currency">BTC/MYR</span>
                        <span class="price">RM 185,420</span>
                        <span class="change positive">+2.4%</span>
                    </div>
                    <div class="mini-chart">
                        <svg viewBox="0 0 200 60">
                            <polyline points="0,50 20,45 40,30 60,35 80,20 100,25 120,15 140,20 160,10 180,15 200,5" 
                                      fill="none" stroke="#4ade80" stroke-width="2"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Enterprise-Grade Features</h2>
                <p>Professional trading tools designed for serious cryptocurrency investors</p>
                <div class="section-badges">
                    <span class="badge">
                        <i class="fas fa-award"></i>
                        Professional Grade
                    </span>
                    <span class="badge">
                        <i class="fas fa-shield-alt"></i>
                        Bank-Level Security
                    </span>
                    <span class="badge">
                        <i class="fas fa-code"></i>
                        Open Source
                    </span>
                </div>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Risk Management</h3>
                    <p>Advanced position sizing, stop-loss, and portfolio protection mechanisms to safeguard your investments.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-area"></i>
                    </div>
                    <h3>Real-time Monitoring</h3>
                    <p>Live dashboard with price charts, trade history, and performance metrics updated in real-time.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h3>Multi-Pair Trading</h3>
                    <p>Support for multiple cryptocurrency pairs on Luno exchange with individual strategy configurations.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3>Easy Configuration</h3>
                    <p>Simple setup wizard and configuration files make it easy to customize trading parameters.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <h3>Simulation Mode</h3>
                    <p>Test your strategies with paper trading before risking real money in live markets.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <h3>Trade History</h3>
                    <p>Comprehensive logging and analysis of all trading activities with detailed performance reports.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="why-choose-us">
        <div class="container">
            <div class="section-header">
                <h2>Why Choose Our Trading Bot?</h2>
                <p>Trusted by traders worldwide for professional cryptocurrency automation</p>
            </div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <div class="comparison-header">
                        <h3>Manual Trading</h3>
                        <div class="comparison-icon manual">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <ul class="comparison-list">
                        <li class="negative"><i class="fas fa-times"></i> Emotional decisions</li>
                        <li class="negative"><i class="fas fa-times"></i> Limited hours</li>
                        <li class="negative"><i class="fas fa-times"></i> Inconsistent execution</li>
                        <li class="negative"><i class="fas fa-times"></i> High stress levels</li>
                        <li class="negative"><i class="fas fa-times"></i> Prone to FOMO</li>
                    </ul>
                </div>
                <div class="comparison-card featured">
                    <div class="comparison-header">
                        <h3>Our Trading Bot</h3>
                        <div class="comparison-icon automated">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="featured-badge">Recommended</div>
                    </div>
                    <ul class="comparison-list">
                        <li class="positive"><i class="fas fa-check"></i> Data-driven decisions</li>
                        <li class="positive"><i class="fas fa-check"></i> 24/7 operation</li>
                        <li class="positive"><i class="fas fa-check"></i> Consistent strategy</li>
                        <li class="positive"><i class="fas fa-check"></i> Stress-free trading</li>
                        <li class="positive"><i class="fas fa-check"></i> Disciplined execution</li>
                    </ul>
                </div>
                <div class="comparison-card">
                    <div class="comparison-header">
                        <h3>Other Bots</h3>
                        <div class="comparison-icon others">
                            <i class="fas fa-cogs"></i>
                        </div>
                    </div>
                    <ul class="comparison-list">
                        <li class="neutral"><i class="fas fa-minus"></i> Expensive subscriptions</li>
                        <li class="neutral"><i class="fas fa-minus"></i> Limited customization</li>
                        <li class="neutral"><i class="fas fa-minus"></i> Closed source</li>
                        <li class="neutral"><i class="fas fa-minus"></i> Vendor lock-in</li>
                        <li class="neutral"><i class="fas fa-minus"></i> Hidden fees</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="statistics">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="stat-number">10,000+</div>
                    <div class="stat-label">Downloads</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-number">4.8/5</div>
                    <div class="stat-label">User Rating</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Countries</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">Uptime</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Setup Section -->
    <section id="setup" class="setup">
        <div class="container">
            <div class="section-header">
                <h2>Quick Setup</h2>
                <p>Get your trading bot running in minutes</p>
            </div>
            <div class="setup-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Clone Repository</h3>
                        <p>Download the trading bot from GitHub</p>
                        <code>git clone https://github.com/amanasmuei/trading-bot-luno.git</code>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Install Dependencies</h3>
                        <p>Run the automated setup script</p>
                        <code>python setup.py</code>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Configure API Keys</h3>
                        <p>Add your Luno API credentials</p>
                        <code>python setup_wizard.py</code>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>Start Trading</h3>
                        <p>Launch the bot and monitor performance</p>
                        <code>python launcher.py</code>
                    </div>
                </div>
            </div>
            <div class="setup-cta">
                <a href="setup.html" class="btn btn-primary">
                    <i class="fas fa-book"></i>
                    Detailed Setup Guide
                </a>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
        <div class="container">
            <div class="section-header">
                <h2>Trusted by Traders Worldwide</h2>
                <p>See what our users are saying about their trading experience</p>
            </div>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"This bot has completely transformed my trading strategy. The risk management features are outstanding, and I love the transparency of the open-source approach."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="author-info">
                            <h4>Sarah Chen</h4>
                            <span>Cryptocurrency Trader</span>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Professional-grade features without the enterprise price tag. The multi-pair support and real-time monitoring have significantly improved my trading results."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="author-info">
                            <h4>Marcus Johnson</h4>
                            <span>Portfolio Manager</span>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Easy setup, excellent documentation, and reliable performance. The simulation mode helped me test strategies before going live. Highly recommended!"</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="author-info">
                            <h4>Alex Rodriguez</h4>
                            <span>Fintech Developer</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq">
        <div class="container">
            <div class="section-header">
                <h2>Frequently Asked Questions</h2>
                <p>Everything you need to know about our trading bot</p>
            </div>
            <div class="faq-grid">
                <div class="faq-item">
                    <h4>Is the trading bot really free?</h4>
                    <p>Yes! Our trading bot is 100% free and open source. There are no subscription fees, hidden costs, or premium tiers. You get access to all features at no cost.</p>
                </div>
                <div class="faq-item">
                    <h4>How secure is my API key and trading data?</h4>
                    <p>Security is our top priority. Your API keys are stored locally on your machine and never transmitted to our servers. The bot uses read-only permissions where possible and implements bank-level encryption.</p>
                </div>
                <div class="faq-item">
                    <h4>Can I customize the trading strategies?</h4>
                    <p>Absolutely! Being open source, you have complete access to modify trading algorithms, risk parameters, and technical indicators. The code is well-documented for easy customization.</p>
                </div>
                <div class="faq-item">
                    <h4>What trading pairs are supported?</h4>
                    <p>We support 15+ trading pairs on Luno including BTC, ETH, LTC, and BCH against major fiat currencies like MYR, ZAR, EUR, GBP, NGN, and UGX.</p>
                </div>
                <div class="faq-item">
                    <h4>Do I need programming knowledge to use it?</h4>
                    <p>Not at all! We provide easy setup scripts, a user-friendly configuration wizard, and comprehensive documentation. Most users can get started in under 5 minutes.</p>
                </div>
                <div class="faq-item">
                    <h4>Can I test strategies before using real money?</h4>
                    <p>Yes! The bot includes a comprehensive simulation mode (paper trading) that lets you test strategies with real market data without risking actual funds.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <div class="cta-content">
                <div class="cta-badge">
                    <i class="fas fa-gift"></i>
                    <span>100% Free & Open Source</span>
                </div>
                <h2>Ready to Transform Your Trading?</h2>
                <p>Join thousands of traders who have automated their cryptocurrency investments with our professional-grade bot</p>
                <div class="cta-features">
                    <div class="cta-feature">
                        <i class="fas fa-check"></i>
                        <span>No subscription fees</span>
                    </div>
                    <div class="cta-feature">
                        <i class="fas fa-check"></i>
                        <span>Complete source code access</span>
                    </div>
                    <div class="cta-feature">
                        <i class="fas fa-check"></i>
                        <span>Professional support community</span>
                    </div>
                </div>
                <div class="cta-buttons">
                    <a href="https://github.com/amanasmuei/trading-bot-luno" class="btn btn-primary" target="_blank">
                        <i class="fab fa-github"></i>
                        Get Started Free
                    </a>
                    <a href="dashboard-demo.html" class="btn btn-secondary">
                        <i class="fas fa-chart-line"></i>
                        Try Demo First
                    </a>
                </div>
                <div class="cta-note">
                    <i class="fas fa-info-circle"></i>
                    <span>No credit card required • Setup in under 5 minutes • Full documentation included</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-robot"></i>
                        <h3>Luno Trading Bot</h3>
                    </div>
                    <p>Enterprise-grade automated trading solution for cryptocurrency markets. Built with professional standards and open-source transparency.</p>
                    <div class="footer-social">
                        <a href="https://github.com/amanasmuei/trading-bot-luno" target="_blank" aria-label="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="https://twitter.com/amanasmuei" target="_blank" aria-label="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://linkedin.com/in/amanasmuei" target="_blank" aria-label="LinkedIn">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Product</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="dashboard-demo.html">Live Demo</a></li>
                        <li><a href="#setup">Quick Setup</a></li>
                        <li><a href="documentation.html">Documentation</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno" target="_blank">Source Code</a></li>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno/releases" target="_blank">Releases</a></li>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno/issues" target="_blank">Bug Reports</a></li>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno/discussions" target="_blank">Community</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="setup.html">Setup Guide</a></li>
                        <li><a href="documentation.html#troubleshooting">Troubleshooting</a></li>
                        <li><a href="https://www.luno.com/api" target="_blank">Luno API Docs</a></li>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno/wiki" target="_blank">Wiki</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p>&copy; 2024 Luno Trading Bot. Open source project under MIT License.</p>
                    <div class="footer-badges">
                        <span class="footer-badge">
                            <i class="fas fa-code"></i>
                            Open Source
                        </span>
                        <span class="footer-badge">
                            <i class="fas fa-shield-check"></i>
                            Secure
                        </span>
                        <span class="footer-badge">
                            <i class="fas fa-heart"></i>
                            Made with Love
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>

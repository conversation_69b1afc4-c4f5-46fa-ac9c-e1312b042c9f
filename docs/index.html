<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luno Trading Bot - Automated Cryptocurrency Trading</title>
    <meta name="description" content="Professional automated trading bot for Luno exchange. Support for multiple trading pairs, risk management, and real-time monitoring.">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-robot"></i>
                <span>Luno Trading Bot</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#features" class="nav-link">Features</a></li>
                <li><a href="#setup" class="nav-link">Setup</a></li>
                <li><a href="documentation.html" class="nav-link">Docs</a></li>
                <li><a href="dashboard-demo.html" class="nav-link">Demo</a></li>
                <li><a href="https://github.com/amanasmuei/trading-bot-luno" class="nav-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="gradient-text">Automated Trading</span><br>
                    Made Simple
                </h1>
                <p class="hero-description">
                    Professional-grade trading bot for Luno exchange with advanced risk management, 
                    real-time monitoring, and support for multiple cryptocurrency pairs.
                </p>
                <div class="hero-buttons">
                    <a href="#setup" class="btn btn-primary">
                        <i class="fas fa-rocket"></i>
                        Get Started
                    </a>
                    <a href="dashboard-demo.html" class="btn btn-secondary">
                        <i class="fas fa-play"></i>
                        View Demo
                    </a>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Monitoring</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">Multi-Pair</span>
                        <span class="stat-label">Support</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">Risk</span>
                        <span class="stat-label">Management</span>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="trading-card">
                    <div class="card-header">
                        <i class="fas fa-chart-line"></i>
                        <span>Live Trading</span>
                        <div class="status-indicator active"></div>
                    </div>
                    <div class="price-display">
                        <span class="currency">BTC/MYR</span>
                        <span class="price">RM 185,420</span>
                        <span class="change positive">+2.4%</span>
                    </div>
                    <div class="mini-chart">
                        <svg viewBox="0 0 200 60">
                            <polyline points="0,50 20,45 40,30 60,35 80,20 100,25 120,15 140,20 160,10 180,15 200,5" 
                                      fill="none" stroke="#4ade80" stroke-width="2"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Powerful Features</h2>
                <p>Everything you need for successful automated trading</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Risk Management</h3>
                    <p>Advanced position sizing, stop-loss, and portfolio protection mechanisms to safeguard your investments.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-area"></i>
                    </div>
                    <h3>Real-time Monitoring</h3>
                    <p>Live dashboard with price charts, trade history, and performance metrics updated in real-time.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h3>Multi-Pair Trading</h3>
                    <p>Support for multiple cryptocurrency pairs on Luno exchange with individual strategy configurations.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3>Easy Configuration</h3>
                    <p>Simple setup wizard and configuration files make it easy to customize trading parameters.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <h3>Simulation Mode</h3>
                    <p>Test your strategies with paper trading before risking real money in live markets.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <h3>Trade History</h3>
                    <p>Comprehensive logging and analysis of all trading activities with detailed performance reports.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Setup Section -->
    <section id="setup" class="setup">
        <div class="container">
            <div class="section-header">
                <h2>Quick Setup</h2>
                <p>Get your trading bot running in minutes</p>
            </div>
            <div class="setup-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Clone Repository</h3>
                        <p>Download the trading bot from GitHub</p>
                        <code>git clone https://github.com/amanasmuei/trading-bot-luno.git</code>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Install Dependencies</h3>
                        <p>Run the automated setup script</p>
                        <code>python setup.py</code>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Configure API Keys</h3>
                        <p>Add your Luno API credentials</p>
                        <code>python setup_wizard.py</code>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>Start Trading</h3>
                        <p>Launch the bot and monitor performance</p>
                        <code>python launcher.py</code>
                    </div>
                </div>
            </div>
            <div class="setup-cta">
                <a href="setup.html" class="btn btn-primary">
                    <i class="fas fa-book"></i>
                    Detailed Setup Guide
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Start Trading?</h2>
                <p>Join the automated trading revolution with our professional-grade bot</p>
                <div class="cta-buttons">
                    <a href="https://github.com/amanasmuei/trading-bot-luno" class="btn btn-primary" target="_blank">
                        <i class="fab fa-github"></i>
                        Download Now
                    </a>
                    <a href="documentation.html" class="btn btn-secondary">
                        <i class="fas fa-book"></i>
                        Read Documentation
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Luno Trading Bot</h3>
                    <p>Professional automated trading solution for cryptocurrency markets.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="setup.html">Setup Guide</a></li>
                        <li><a href="documentation.html">Documentation</a></li>
                        <li><a href="dashboard-demo.html">Demo</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno" target="_blank">GitHub Repository</a></li>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno/issues" target="_blank">Report Issues</a></li>
                        <li><a href="https://www.luno.com/api" target="_blank">Luno API</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Luno Trading Bot. Open source project.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>

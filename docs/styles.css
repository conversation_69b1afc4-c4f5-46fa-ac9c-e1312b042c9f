/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e7eb;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.nav-logo i {
    margin-right: 10px;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #4b5563;
    font-weight: 500;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    color: #667eea;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #4b5563;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.gradient-text {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #fbbf24;
    color: #1f2937;
}

.btn-primary:hover {
    background: #f59e0b;
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.hero-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #fbbf24;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Trading Card */
.hero-visual {
    display: flex;
    justify-content: center;
}

.trading-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 350px;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.card-header i {
    color: #fbbf24;
    margin-right: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.price-display {
    margin-bottom: 20px;
}

.currency {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 4px;
}

.price {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 4px;
}

.change {
    font-size: 0.9rem;
    font-weight: 600;
}

.change.positive {
    color: #10b981;
}

.mini-chart {
    height: 60px;
}

.mini-chart svg {
    width: 100%;
    height: 100%;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: #f9fafb;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
}

.section-header p {
    font-size: 1.25rem;
    color: #6b7280;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.feature-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Setup Section */
.setup {
    padding: 80px 0;
    background: white;
}

.setup-steps {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;
}

.step {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    background: #f9fafb;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.step-content p {
    color: #6b7280;
    margin-bottom: 1rem;
}

.step-content code {
    background: #1f2937;
    color: #fbbf24;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
    display: block;
    overflow-x: auto;
}

.setup-cta {
    text-align: center;
}

/* CTA Section */
.cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #fbbf24;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #fbbf24;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* Page Header */
.page-header {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    color: white;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-header h1 i {
    color: #fbbf24;
    margin-right: 1rem;
}

.page-header p {
    font-size: 1.25rem;
    opacity: 0.9;
}

/* Setup Content */
.setup-content {
    padding: 80px 0;
    background: #f9fafb;
}

.setup-content .container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 3rem;
    max-width: 1200px;
}

.setup-nav {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.setup-nav h3 {
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.1rem;
}

.setup-nav ul {
    list-style: none;
}

.setup-nav ul li {
    margin-bottom: 0.5rem;
}

.setup-nav ul li a {
    color: #6b7280;
    text-decoration: none;
    padding: 0.5rem 0;
    display: block;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.setup-nav ul li a:hover {
    color: #667eea;
    background: #f3f4f6;
    padding-left: 1rem;
}

.setup-main {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.setup-section {
    padding: 3rem;
    border-bottom: 1px solid #e5e7eb;
}

.setup-section:last-child {
    border-bottom: none;
}

.setup-section h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.setup-section h2 i {
    color: #667eea;
}

/* Requirements Grid */
.requirement-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.requirement-card {
    background: #f9fafb;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.requirement-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.requirement-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.requirement-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.requirement-card p {
    color: #6b7280;
    margin-bottom: 1rem;
}

.requirement-card a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.requirement-card a:hover {
    text-decoration: underline;
}

/* Step Container */
.step-container {
    margin-top: 2rem;
}

.step-item {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    padding-bottom: 3rem;
    border-bottom: 1px solid #e5e7eb;
}

.step-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.step-number {
    width: 50px;
    height: 50px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.step-content p {
    color: #6b7280;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.step-content pre {
    background: #1f2937;
    color: #e5e7eb;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1rem 0;
}

.step-content code {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
}

/* Info and Warning Boxes */
.info-box, .warning-box {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin: 1rem 0;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-box {
    background: #dbeafe;
    border-left: 4px solid #3b82f6;
}

.info-box i {
    color: #3b82f6;
    margin-top: 0.2rem;
}

.warning-box {
    background: #fef3c7;
    border-left: 4px solid #f59e0b;
}

.warning-box i {
    color: #f59e0b;
    margin-top: 0.2rem;
}

.info-box p, .warning-box p {
    margin: 0;
    color: #1f2937;
}

/* Parameter Grid */
.param-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.param-card {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.param-card h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
    font-family: monospace;
    background: #e5e7eb;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
}

.param-card p {
    color: #6b7280;
    font-size: 0.9rem;
}

/* API Steps */
.api-steps {
    margin-top: 2rem;
}

.api-step {
    margin-bottom: 2rem;
    padding: 2rem;
    background: #f9fafb;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.api-step h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.api-step ol {
    margin-left: 1.5rem;
    color: #6b7280;
}

.api-step ol li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Run Options */
.run-options {
    display: grid;
    gap: 2rem;
    margin-top: 2rem;
}

.run-option {
    background: #f9fafb;
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.run-option h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.run-option h3 i {
    color: #667eea;
}

/* Troubleshooting Grid */
.troubleshoot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.trouble-item {
    background: #f9fafb;
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid #ef4444;
}

.trouble-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.trouble-item ul {
    margin-left: 1.5rem;
    color: #6b7280;
}

.trouble-item ul li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.trouble-item code {
    background: #e5e7eb;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.85rem;
}

/* Active Navigation Link */
.nav-link.active {
    color: #667eea;
    font-weight: 600;
}

/* Demo Header */
.demo-header {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.demo-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.demo-header h1 i {
    color: #fbbf24;
    margin-right: 1rem;
}

.demo-header p {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.demo-notice {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 2rem;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    backdrop-filter: blur(10px);
}

.demo-notice i {
    color: #fbbf24;
}

/* Dashboard Demo */
.dashboard-demo {
    padding: 80px 0;
    background: #f5f5f5;
}

.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.dashboard-header h2 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.dashboard-header p {
    margin: 0;
    opacity: 0.9;
}

.dashboard-controls {
    display: flex;
    gap: 1rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dashboard-card h3 {
    margin: 0 0 1.5rem 0;
    color: #1f2937;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.dashboard-card h3 i {
    color: #667eea;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-running {
    background-color: #10b981;
    animation: pulse 2s infinite;
}

.status-stopped {
    background-color: #ef4444;
}

.price-display {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    text-align: center;
    margin: 1rem 0;
}

.metric {
    display: flex;
    justify-content: space-between;
    margin: 0.75rem 0;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.metric:last-child {
    border-bottom: none;
}

.metric span:first-child {
    color: #6b7280;
    font-weight: 500;
}

.metric span:last-child {
    color: #1f2937;
    font-weight: 600;
}

.positive {
    color: #10b981 !important;
}

.negative {
    color: #ef4444 !important;
}

.trades-list {
    max-height: 300px;
    overflow-y: auto;
}

.trade-item {
    padding: 0.75rem;
    margin: 0.5rem 0;
    border-radius: 6px;
    background-color: #f9fafb;
    font-size: 0.9rem;
    border-left: 3px solid #e5e7eb;
}

.trade-item.simulated {
    background-color: #fef3c7;
    border-left-color: #f59e0b;
}

.trade-item div:first-child {
    color: #6b7280;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.trade-item div:last-child {
    color: #1f2937;
    font-weight: 500;
}

.chart-container {
    grid-column: 1 / -1;
    min-height: 400px;
}

.refresh-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background 0.3s ease;
}

.refresh-btn:hover {
    background: #5a67d8;
}

/* Trading Signals */
.signal-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    margin: 0.5rem 0;
    background: #f9fafb;
    border-radius: 6px;
}

.signal-type {
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 60px;
    text-align: center;
}

.signal-type.buy {
    background: #dcfce7;
    color: #166534;
}

.signal-type.sell {
    background: #fee2e2;
    color: #991b1b;
}

.signal-type.neutral {
    background: #f3f4f6;
    color: #374151;
}

/* Demo Features */
.demo-features {
    padding: 80px 0;
    background: white;
}

.demo-features h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #1f2937;
}

.demo-features .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
    background: #f9fafb;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-4px);
}

.feature-item i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-item h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.feature-item p {
    color: #6b7280;
    line-height: 1.6;
}

/* Documentation Styles */
.docs-content {
    padding: 80px 0;
    background: #f9fafb;
}

.docs-content .container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 3rem;
    max-width: 1200px;
}

.docs-nav {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.docs-nav h3 {
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.1rem;
}

.docs-nav ul {
    list-style: none;
}

.docs-nav ul li {
    margin-bottom: 0.5rem;
}

.docs-nav ul li a {
    color: #6b7280;
    text-decoration: none;
    padding: 0.5rem 0;
    display: block;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.docs-nav ul li a:hover {
    color: #667eea;
    background: #f3f4f6;
    padding-left: 1rem;
}

.docs-main {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.docs-section {
    padding: 3rem;
    border-bottom: 1px solid #e5e7eb;
}

.docs-section:last-child {
    border-bottom: none;
}

.docs-section h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.docs-section h2 i {
    color: #667eea;
}

.docs-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 2rem 0 1rem 0;
    color: #1f2937;
}

.docs-section h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1.5rem 0 0.5rem 0;
    color: #1f2937;
}

.docs-section p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.docs-section ul, .docs-section ol {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.docs-section li {
    margin-bottom: 0.5rem;
}

.docs-section pre {
    background: #1f2937;
    color: #e5e7eb;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1rem 0;
}

.docs-section code {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
}

.docs-section p code {
    background: #e5e7eb;
    color: #1f2937;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

/* Configuration Table */
.config-table {
    overflow-x: auto;
    margin: 1.5rem 0;
}

.config-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-table th,
.config-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.config-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #1f2937;
}

.config-table td {
    color: #6b7280;
}

.config-table td code {
    background: #e5e7eb;
    color: #1f2937;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

/* API Endpoints */
.api-endpoint {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1.5rem 0;
    border-left: 4px solid #667eea;
}

.api-endpoint h4 {
    font-family: monospace;
    background: #1f2937;
    color: #fbbf24;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    margin: 0 0 1rem 0;
    font-size: 1rem;
}

.api-endpoint p {
    margin-bottom: 1rem;
}

/* Strategy Cards */
.strategy-card {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1.5rem 0;
    border-left: 4px solid #10b981;
}

.strategy-card h4 {
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.strategy-card p {
    margin-bottom: 1rem;
}

.strategy-card ul {
    margin: 0;
}

/* FAQ Items */
.faq-item {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1.5rem 0;
    border-left: 4px solid #3b82f6;
}

.faq-item h4 {
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.faq-item p {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .step {
        flex-direction: column;
        text-align: center;
    }

    .setup-content .container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .setup-nav {
        position: static;
        order: 2;
    }

    .setup-main {
        order: 1;
    }

    .step-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .setup-section {
        padding: 2rem 1.5rem;
    }
}

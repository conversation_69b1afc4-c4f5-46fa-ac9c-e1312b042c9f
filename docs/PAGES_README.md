# Luno Trading Bot - GitHub Pages

This directory contains the GitHub Pages website for the Luno Trading Bot project.

## 🌐 Live Website

Visit the live website at: [https://amanasmuei.github.io/trading-bot-luno](https://amanasmuei.github.io/trading-bot-luno)

## 📁 Website Structure

- **`index.html`** - Main landing page with features and overview
- **`setup.html`** - Complete setup and installation guide
- **`documentation.html`** - Comprehensive documentation and API reference
- **`dashboard-demo.html`** - Interactive dashboard demonstration
- **`styles.css`** - Main stylesheet for all pages
- **`script.js`** - JavaScript for interactivity and animations
- **`dashboard-demo.js`** - JavaScript for the dashboard demo functionality
- **`_config.yml`** - Jekyll configuration for GitHub Pages

## 🚀 Features

### Landing Page
- Professional hero section with animated elements
- Feature showcase with interactive cards
- Quick setup guide with code examples
- Call-to-action sections

### Setup Guide
- Step-by-step installation instructions
- Prerequisites and requirements
- API configuration guide
- Troubleshooting section

### Documentation
- Complete API reference
- Configuration parameters
- Trading strategies explanation
- Risk management guidelines
- FAQ section

### Dashboard Demo
- Interactive trading dashboard simulation
- Real-time price updates (simulated)
- Live charts with Plotly.js
- Trading signals and portfolio tracking

## 🎨 Design Features

- **Responsive Design**: Works on all devices and screen sizes
- **Modern UI**: Clean, professional design with smooth animations
- **Interactive Elements**: Hover effects, animations, and transitions
- **Code Highlighting**: Syntax highlighting for code examples
- **Mobile Navigation**: Hamburger menu for mobile devices

## 🛠️ Development

### Local Development

To run the website locally:

```bash
# Clone the repository
git clone https://github.com/amanasmuei/trading-bot-luno.git
cd trading-bot-luno/docs

# Serve locally (if you have Jekyll installed)
jekyll serve

# Or simply open index.html in your browser
open index.html
```

### GitHub Pages Setup

The website is automatically deployed to GitHub Pages when changes are pushed to the `docs/` directory in the main branch.

To enable GitHub Pages:
1. Go to repository Settings
2. Scroll to "Pages" section
3. Select "Deploy from a branch"
4. Choose "main" branch and "/docs" folder
5. Save the configuration

## 📱 Responsive Breakpoints

- **Desktop**: 1200px and above
- **Tablet**: 768px to 1199px
- **Mobile**: Below 768px

## 🎯 SEO Optimization

- Meta descriptions and titles
- Open Graph tags for social sharing
- Structured data markup
- Sitemap generation
- Fast loading times

## 🔧 Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with Flexbox and Grid
- **JavaScript**: Interactive functionality
- **Plotly.js**: Interactive charts
- **Font Awesome**: Icons
- **Google Fonts**: Typography (Inter font family)
- **Prism.js**: Code syntax highlighting
- **Jekyll**: Static site generation

## 📊 Analytics

The website includes provisions for Google Analytics tracking (currently commented out in `_config.yml`).

## 🤝 Contributing

To contribute to the website:

1. Fork the repository
2. Create a feature branch
3. Make your changes in the `docs/` directory
4. Test locally
5. Submit a pull request

## 📄 License

This website and documentation are part of the Luno Trading Bot project and follow the same license terms.

## 🔗 Links

- [Main Repository](https://github.com/amanasmuei/trading-bot-luno)
- [Live Website](https://amanasmuei.github.io/trading-bot-luno)
- [Issues](https://github.com/amanasmuei/trading-bot-luno/issues)
- [Luno API](https://www.luno.com/api)

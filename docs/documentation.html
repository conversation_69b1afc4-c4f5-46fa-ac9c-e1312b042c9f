<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation - Luno Trading Bot</title>
    <meta name="description" content="Complete documentation for the Luno Trading Bot including API reference, configuration options, and usage examples.">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-robot"></i>
                <span>Luno Trading Bot</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="index.html#features" class="nav-link">Features</a></li>
                <li><a href="setup.html" class="nav-link">Setup</a></li>
                <li><a href="documentation.html" class="nav-link active">Docs</a></li>
                <li><a href="dashboard-demo.html" class="nav-link">Demo</a></li>
                <li><a href="https://github.com/amanasmuei/trading-bot-luno" class="nav-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="page-header">
        <div class="container">
            <h1><i class="fas fa-book"></i> Documentation</h1>
            <p>Complete guide to using the Luno Trading Bot</p>
        </div>
    </section>

    <!-- Content -->
    <section class="docs-content">
        <div class="container">
            <div class="docs-nav">
                <h3>Documentation</h3>
                <ul>
                    <li><a href="#overview">Overview</a></li>
                    <li><a href="#configuration">Configuration</a></li>
                    <li><a href="#api-reference">API Reference</a></li>
                    <li><a href="#trading-strategies">Trading Strategies</a></li>
                    <li><a href="#risk-management">Risk Management</a></li>
                    <li><a href="#monitoring">Monitoring</a></li>
                    <li><a href="#examples">Examples</a></li>
                    <li><a href="#faq">FAQ</a></li>
                </ul>
            </div>

            <div class="docs-main">
                <!-- Overview -->
                <section id="overview" class="docs-section">
                    <h2><i class="fas fa-info-circle"></i> Overview</h2>
                    <p>The Luno Trading Bot is a professional-grade automated trading solution designed for the Luno cryptocurrency exchange. It provides advanced features for algorithmic trading, risk management, and portfolio monitoring.</p>
                    
                    <h3>Key Features</h3>
                    <ul>
                        <li><strong>Multi-pair Support:</strong> Trade multiple cryptocurrency pairs simultaneously</li>
                        <li><strong>Risk Management:</strong> Built-in stop-loss, position sizing, and portfolio protection</li>
                        <li><strong>Real-time Monitoring:</strong> Live dashboard with charts and performance metrics</li>
                        <li><strong>Simulation Mode:</strong> Test strategies without risking real money</li>
                        <li><strong>Flexible Configuration:</strong> Customizable trading parameters and strategies</li>
                    </ul>

                    <h3>Architecture</h3>
                    <p>The bot consists of several key components:</p>
                    <ul>
                        <li><strong>Trading Engine:</strong> Core logic for executing trades and managing positions</li>
                        <li><strong>API Client:</strong> Interface to Luno exchange API</li>
                        <li><strong>Risk Manager:</strong> Monitors and controls trading risks</li>
                        <li><strong>Web Dashboard:</strong> Real-time monitoring interface</li>
                        <li><strong>Data Storage:</strong> Trade history and performance tracking</li>
                    </ul>
                </section>

                <!-- Configuration -->
                <section id="configuration" class="docs-section">
                    <h2><i class="fas fa-cog"></i> Configuration</h2>
                    
                    <h3>Environment Variables</h3>
                    <p>Configure the bot using environment variables in a <code>.env</code> file:</p>
                    
                    <pre><code class="language-bash"># Luno API Configuration
LUNO_API_KEY=your_api_key_here
LUNO_API_SECRET=your_api_secret_here

# Trading Configuration
TRADING_PAIR=XBTMYR
DRY_RUN=true
MAX_POSITION_SIZE=10
STOP_LOSS_PERCENTAGE=5
TAKE_PROFIT_PERCENTAGE=10

# Risk Management
MAX_DAILY_LOSS=1000
MAX_OPEN_POSITIONS=3
MIN_TRADE_AMOUNT=0.001

# Web Dashboard
WEB_PORT=5000
WEB_HOST=localhost
WEB_DEBUG=false

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log</code></pre>

                    <h3>Configuration Parameters</h3>
                    <div class="config-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>Parameter</th>
                                    <th>Type</th>
                                    <th>Default</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>TRADING_PAIR</code></td>
                                    <td>string</td>
                                    <td>XBTMYR</td>
                                    <td>Cryptocurrency pair to trade</td>
                                </tr>
                                <tr>
                                    <td><code>DRY_RUN</code></td>
                                    <td>boolean</td>
                                    <td>true</td>
                                    <td>Enable simulation mode</td>
                                </tr>
                                <tr>
                                    <td><code>MAX_POSITION_SIZE</code></td>
                                    <td>number</td>
                                    <td>10</td>
                                    <td>Maximum position size as percentage of portfolio</td>
                                </tr>
                                <tr>
                                    <td><code>STOP_LOSS_PERCENTAGE</code></td>
                                    <td>number</td>
                                    <td>5</td>
                                    <td>Stop loss threshold percentage</td>
                                </tr>
                                <tr>
                                    <td><code>TAKE_PROFIT_PERCENTAGE</code></td>
                                    <td>number</td>
                                    <td>10</td>
                                    <td>Take profit threshold percentage</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- API Reference -->
                <section id="api-reference" class="docs-section">
                    <h2><i class="fas fa-code"></i> API Reference</h2>
                    
                    <h3>Bot Control</h3>
                    <div class="api-endpoint">
                        <h4>GET /api/bot_status</h4>
                        <p>Get current bot status and configuration</p>
                        <pre><code class="language-json">{
  "success": true,
  "data": {
    "running": true,
    "config": {
      "trading_pair": "XBTMYR",
      "dry_run": true,
      "max_position_size": 10
    }
  }
}</code></pre>
                    </div>

                    <div class="api-endpoint">
                        <h4>POST /api/start_bot</h4>
                        <p>Start the trading bot</p>
                        <pre><code class="language-json">{
  "success": true,
  "message": "Bot started successfully"
}</code></pre>
                    </div>

                    <div class="api-endpoint">
                        <h4>POST /api/stop_bot</h4>
                        <p>Stop the trading bot</p>
                        <pre><code class="language-json">{
  "success": true,
  "message": "Bot stopped successfully"
}</code></pre>
                    </div>

                    <h3>Market Data</h3>
                    <div class="api-endpoint">
                        <h4>GET /api/market_data</h4>
                        <p>Get current market data for the configured trading pair</p>
                        <pre><code class="language-json">{
  "success": true,
  "data": {
    "price": 185420.50,
    "bid": 185380.00,
    "ask": 185460.00,
    "volume": 142.35,
    "timestamp": "2024-01-15T14:30:00Z"
  }
}</code></pre>
                    </div>

                    <h3>Portfolio</h3>
                    <div class="api-endpoint">
                        <h4>GET /api/portfolio</h4>
                        <p>Get current portfolio balances</p>
                        <pre><code class="language-json">{
  "success": true,
  "data": {
    "BTC": {
      "total": 0.125000,
      "available": 0.120000,
      "reserved": 0.005000
    },
    "MYR": {
      "total": 5420.50,
      "available": 5420.50,
      "reserved": 0.00
    }
  }
}</code></pre>
                    </div>
                </section>

                <!-- Trading Strategies -->
                <section id="trading-strategies" class="docs-section">
                    <h2><i class="fas fa-chart-line"></i> Trading Strategies</h2>
                    
                    <h3>Built-in Strategies</h3>
                    <p>The bot includes several pre-built trading strategies:</p>
                    
                    <div class="strategy-card">
                        <h4>Mean Reversion</h4>
                        <p>Buys when price is below moving average and sells when above. Good for ranging markets.</p>
                        <ul>
                            <li>Uses 20-period moving average</li>
                            <li>RSI confirmation (oversold/overbought)</li>
                            <li>Volume confirmation</li>
                        </ul>
                    </div>

                    <div class="strategy-card">
                        <h4>Momentum</h4>
                        <p>Follows price trends and momentum. Suitable for trending markets.</p>
                        <ul>
                            <li>MACD signal crossovers</li>
                            <li>Breakout detection</li>
                            <li>Trend confirmation</li>
                        </ul>
                    </div>

                    <div class="strategy-card">
                        <h4>Grid Trading</h4>
                        <p>Places buy and sell orders at regular intervals. Works well in sideways markets.</p>
                        <ul>
                            <li>Configurable grid spacing</li>
                            <li>Dynamic grid adjustment</li>
                            <li>Profit taking at each level</li>
                        </ul>
                    </div>

                    <h3>Custom Strategies</h3>
                    <p>You can implement custom trading strategies by extending the base strategy class:</p>
                    <pre><code class="language-python">from src.bot.strategy import BaseStrategy

class MyCustomStrategy(BaseStrategy):
    def should_buy(self, market_data):
        # Implement your buy logic
        return True
    
    def should_sell(self, market_data, position):
        # Implement your sell logic
        return False</code></pre>
                </section>

                <!-- Risk Management -->
                <section id="risk-management" class="docs-section">
                    <h2><i class="fas fa-shield-alt"></i> Risk Management</h2>
                    
                    <h3>Position Sizing</h3>
                    <p>The bot automatically calculates position sizes based on your risk parameters:</p>
                    <ul>
                        <li><strong>Fixed Percentage:</strong> Use a fixed percentage of portfolio per trade</li>
                        <li><strong>Kelly Criterion:</strong> Optimal position sizing based on win rate and average returns</li>
                        <li><strong>Volatility-based:</strong> Adjust position size based on market volatility</li>
                    </ul>

                    <h3>Stop Loss</h3>
                    <p>Multiple stop loss mechanisms protect your capital:</p>
                    <ul>
                        <li><strong>Percentage Stop:</strong> Exit when loss exceeds specified percentage</li>
                        <li><strong>ATR Stop:</strong> Dynamic stop based on Average True Range</li>
                        <li><strong>Time Stop:</strong> Exit after specified time period</li>
                    </ul>

                    <h3>Portfolio Protection</h3>
                    <ul>
                        <li><strong>Daily Loss Limit:</strong> Stop trading if daily losses exceed threshold</li>
                        <li><strong>Maximum Positions:</strong> Limit number of concurrent positions</li>
                        <li><strong>Correlation Limits:</strong> Avoid over-concentration in correlated assets</li>
                    </ul>
                </section>

                <!-- Monitoring -->
                <section id="monitoring" class="docs-section">
                    <h2><i class="fas fa-chart-area"></i> Monitoring</h2>
                    
                    <h3>Web Dashboard</h3>
                    <p>Access the real-time dashboard at <code>http://localhost:5000</code> to monitor:</p>
                    <ul>
                        <li>Bot status and configuration</li>
                        <li>Current market prices and trends</li>
                        <li>Portfolio balances and performance</li>
                        <li>Recent trades and order history</li>
                        <li>Interactive price charts</li>
                    </ul>

                    <h3>Logging</h3>
                    <p>The bot maintains detailed logs of all activities:</p>
                    <pre><code class="language-bash"># View live logs
tail -f logs/trading_bot.log

# Search for specific events
grep "TRADE" logs/trading_bot.log

# View error logs
grep "ERROR" logs/trading_bot.log</code></pre>

                    <h3>Alerts</h3>
                    <p>Configure alerts for important events:</p>
                    <ul>
                        <li>Trade executions</li>
                        <li>Stop loss triggers</li>
                        <li>API connection issues</li>
                        <li>Daily loss limits reached</li>
                    </ul>
                </section>

                <!-- Examples -->
                <section id="examples" class="docs-section">
                    <h2><i class="fas fa-code"></i> Examples</h2>
                    
                    <h3>Basic Usage</h3>
                    <pre><code class="language-bash"># Start the bot with default configuration
python launcher.py

# Start in simulation mode
DRY_RUN=true python launcher.py

# Start with custom trading pair
TRADING_PAIR=ETHMYR python launcher.py</code></pre>

                    <h3>Custom Configuration</h3>
                    <pre><code class="language-bash"># Create custom .env file
cat > .env << EOF
LUNO_API_KEY=your_key
LUNO_API_SECRET=your_secret
TRADING_PAIR=XBTMYR
DRY_RUN=false
MAX_POSITION_SIZE=5
STOP_LOSS_PERCENTAGE=3
EOF

# Run with custom configuration
python launcher.py</code></pre>
                </section>

                <!-- FAQ -->
                <section id="faq" class="docs-section">
                    <h2><i class="fas fa-question-circle"></i> FAQ</h2>
                    
                    <div class="faq-item">
                        <h4>Q: Is it safe to run the bot with real money?</h4>
                        <p>A: Always start with simulation mode (DRY_RUN=true) to test your configuration. Only use real money after thorough testing and understanding the risks involved.</p>
                    </div>

                    <div class="faq-item">
                        <h4>Q: What trading pairs are supported?</h4>
                        <p>A: The bot supports all trading pairs available on Luno exchange, including XBTMYR, ETHMYR, XBTZAR, ETHZAR, and others.</p>
                    </div>

                    <div class="faq-item">
                        <h4>Q: How much capital do I need to start?</h4>
                        <p>A: There's no minimum requirement, but we recommend starting with at least the equivalent of $100-500 to allow for proper position sizing and risk management.</p>
                    </div>

                    <div class="faq-item">
                        <h4>Q: Can I run multiple bots simultaneously?</h4>
                        <p>A: Yes, you can run multiple instances with different configurations and trading pairs, but ensure you have sufficient API rate limits and capital allocation.</p>
                    </div>

                    <div class="faq-item">
                        <h4>Q: How do I backup my trading data?</h4>
                        <p>A: All trading data is stored in the <code>logs/</code> directory. Regularly backup this folder to preserve your trading history and performance data.</p>
                    </div>
                </section>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Luno Trading Bot</h3>
                    <p>Professional automated trading solution for cryptocurrency markets.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#features">Features</a></li>
                        <li><a href="setup.html">Setup Guide</a></li>
                        <li><a href="dashboard-demo.html">Demo</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno" target="_blank">GitHub Repository</a></li>
                        <li><a href="https://github.com/amanasmuei/trading-bot-luno/issues" target="_blank">Report Issues</a></li>
                        <li><a href="https://www.luno.com/api" target="_blank">Luno API</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Luno Trading Bot. Open source project.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="script.js"></script>
</body>
</html>

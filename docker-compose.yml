version: '3.8'

services:
  luno-trading-bot:
    build: .
    container_name: luno-trading-bot
    ports:
      - "5000:5000"
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a monitoring service
  # watchtower:
  #   image: containrrr/watchtower
  #   container_name: watchtower
  #   volumes:
  #     - /var/run/docker.sock:/var/run/docker.sock
  #   command: --interval 30 --cleanup
  #   restart: unless-stopped
